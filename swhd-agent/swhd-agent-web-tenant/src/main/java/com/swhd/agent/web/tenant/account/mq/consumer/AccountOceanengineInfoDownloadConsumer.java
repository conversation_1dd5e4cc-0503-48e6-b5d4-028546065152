package com.swhd.agent.web.tenant.account.mq.consumer;

import com.swhd.agent.api.account.client.AccountOceanengineOptLogClient;
import com.swhd.agent.api.account.dto.param.log.AccountOceanengineOptLogPageStatisticsParam;
import com.swhd.agent.api.account.dto.result.AccountOceanengineOptLogStatisticsResult;
import com.swhd.agent.web.tenant.account.properties.AccountProperties;
import com.swhd.agent.web.tenant.account.service.AccountOceanengineInfoService;
import com.swhd.agent.web.tenant.account.vo.poi.AccountOceanengineOptLogStatisticsResultPoiExcelVo;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineInfoResultVo;
import com.swhd.content.api.download.dto.message.DownloadRecordAddMessage;
import com.swhd.content.api.download.dto.result.DownloadExportRecordResult;
import com.swhd.content.api.download.util.DownloadExportRecordUtil;
import com.swhd.content.api.download.util.DownloadPoiExcelUtil;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.easypoi.entity.EasyExcelData;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.bean.BeanUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Slf4j
@Component
@AllArgsConstructor
public class AccountOceanengineContentDownloadConsumer {

    public static final String ACCOUNT_OCEANENGINE_OPT_LOG_STATISTICS = "account_oceanengine_opt_log_statistics";

    private final AccountProperties accountProperties;

    private final AccountOceanengineOptLogClient accountOceanengineOptLogClient;

    private final AccountOceanengineInfoService accountOceanengineInfoService;

    @Bean
    public Consumer<DownloadRecordAddMessage> agentContentDownloadRecordAddAccount() {
        return message -> TenantHolder.methodTenantVoid(message.getTenantId(), () -> {
            try {
                switch (message.getExportType()) {
                    case ACCOUNT_OCEANENGINE_OPT_LOG_STATISTICS -> {
                        log.info("消费巨量引擎账号操作日志统计下载消息：{}", JsonLogUtil.toJsonString(message));
                        DownloadExportRecordUtil.exportRecord(message.getId(), this::accountOceanengineOptLogStatistics);
                    }
                }
            } catch (Throwable e) {
                log.error("下载异常", e);
                DownloadExportRecordUtil.fail(message.getId());
            }
        });
    }

    /**
     * 账号操作日志统计下载
     */
    private void accountOceanengineOptLogStatistics(DownloadExportRecordResult exportRecord) throws IOException {
        AccountOceanengineOptLogPageStatisticsParam param = JsonUtil.convertValue(exportRecord.getExportParams(),
                AccountOceanengineOptLogPageStatisticsParam.class);
        param.setCurrent(1);
        param.setSize(500);

        Rsp<PageResult<AccountOceanengineOptLogStatisticsResult>> pageRsp = accountOceanengineOptLogClient.pageStatistics(param);
        RspHd.failThrowException(pageRsp);
        List<AccountOceanengineOptLogStatisticsResultPoiExcelVo> voList = Func.copy(pageRsp.getData().getRecords(), AccountOceanengineOptLogStatisticsResultPoiExcelVo.class);
        List<AccountOceanengineInfoResultVo> accountOceanengineInfoResultVoList = Func.copy(voList, AccountOceanengineInfoResultVo.class);
        accountOceanengineInfoService.fillAccountInfo(accountOceanengineInfoResultVoList);
        Map<Long, AccountOceanengineInfoResultVo> advertiserIdVoMap = accountOceanengineInfoResultVoList.stream().collect(Collectors.toMap(AccountOceanengineInfoResultVo::getAdvertiserId, Function.identity(), (o1, o2) -> o2));
        voList.forEach(vo -> BeanUtil.copyNonNull(advertiserIdVoMap.get(vo.getAdvertiserId()), vo));

        EasyExcelData data = new EasyExcelData();
        data.addFillIndexList(voList);
        DownloadPoiExcelUtil.uploadOss(exportRecord, accountProperties.getOpLogStatisticsExcelTemplate(), EasyExcelData.indexList(voList));
    }

}
