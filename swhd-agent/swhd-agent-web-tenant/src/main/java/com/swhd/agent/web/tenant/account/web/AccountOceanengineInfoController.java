package com.swhd.agent.web.tenant.account.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.agent.api.account.client.AccountOceanengineInfoClient;
import com.swhd.agent.api.account.dto.param.info.*;
import com.swhd.agent.api.account.dto.result.AccountOceanengineInfoResult;
import com.swhd.agent.web.tenant.account.mq.consumer.AccountOceanengineContentDownloadConsumer;
import com.swhd.agent.web.tenant.account.service.AccountOceanengineInfoService;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineInfoResultVo;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.content.api.download.client.DownloadExportRecordClient;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordAddParam;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/accountOceanengineInfo")
public class AccountOceanengineInfoController {

    private final AccountOceanengineInfoClient accountOceanengineInfoClient;

    private final AccountOceanengineInfoService accountOceanengineInfoService;

    private final DownloadExportRecordClient downloadExportRecordClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<AccountOceanengineInfoResultVo>> page(@RequestBody @Valid AccountOceanengineInfoPageParam param) {
        Rsp<PageResult<AccountOceanengineInfoResult>> pageRsp = accountOceanengineInfoClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<AccountOceanengineInfoResultVo> voPageResult = PageUtil.convert(pageRsp.getData(), AccountOceanengineInfoResultVo.class);
        if (Func.isEmpty(voPageResult.getRecords())) {
            return RspHd.data(voPageResult);
        }
        accountOceanengineInfoService.fillAccountInfo(voPageResult.getRecords());
        return RspHd.data(voPageResult);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<AccountOceanengineInfoResult> getById(@RequestParam("id") Long id) {
        return accountOceanengineInfoClient.getById(id);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid AccountOceanengineInfoUpdateParam param) {
        return accountOceanengineInfoClient.update(param);
    }

    @Operation(summary = "批量修改")
    @PostMapping("/batchUpdate")
    public Rsp<Void> batchUpdate(@RequestBody @Valid AccountOceanengineInfoBatchUpdateParam param) {
        return accountOceanengineInfoClient.batchUpdate(param);
    }

    @Operation(summary = "根据advertiserIds批量修改")
    @PostMapping("/batchUpdateByAdvertiserIds")
    Rsp<Void> batchUpdateByAdvertiserIds(@RequestBody @Valid AccountOceanengineInfoBatchUpdateByAdvertiserIdsParam param){
        return accountOceanengineInfoClient.batchUpdateByAdvertiserIds(param);
    }

    @Operation(summary = "根据客户ID批量修改")
    @PostMapping("/batchUpdateByCustomId")
    public Rsp<Void> batchUpdateByCustomId(@RequestBody @Valid AccountOceanengineInfoBatchUpdateByCustomIdParam param) {
        return accountOceanengineInfoClient.batchUpdateByCustomId(param);
    }

    @Operation(summary = "列表查询")
    @PostMapping("/list")
    public Rsp<List<AccountOceanengineInfoResultVo>> list(@RequestBody @Valid AccountOceanengineInfoListParam param) {
        Rsp<List<AccountOceanengineInfoResult>> listRsp = accountOceanengineInfoClient.list(param);
        if (RspHd.isFail(listRsp)) {
            return RspHd.fail(listRsp);
        }
        List<AccountOceanengineInfoResultVo> voList = Func.copy(listRsp.getData(), AccountOceanengineInfoResultVo.class);
        if (Func.isEmpty(voList)) {
            return RspHd.data(voList);
        }
        accountOceanengineInfoService.fillAccountInfo(voList);
        return RspHd.data(voList);
    }

    @Operation(summary = "下载")
    @PostMapping("/download")
    public Rsp<Void> download(@RequestBody @Valid AccountOceanengineInfoListParam param) {
        DownloadExportRecordAddParam exportRecordAddParam = new DownloadExportRecordAddParam();
        exportRecordAddParam.setUserId(CurrentUserHolder.currentUserId());
        exportRecordAddParam.setExportType(AccountOceanengineContentDownloadConsumer.ACCOUNT_OCEANENGINE_INFO);
        exportRecordAddParam.setExportParams(JsonUtil.convertValue(param, JsonNode.class));
        return downloadExportRecordClient.add(exportRecordAddParam);
    }

}
