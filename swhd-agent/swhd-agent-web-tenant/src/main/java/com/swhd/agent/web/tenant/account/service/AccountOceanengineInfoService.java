package com.swhd.agent.web.tenant.account.service;

import com.swhd.agent.api.account.wrapper.AccountOceanengineInfoWrapper;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineInfoResultVo;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineInfoSet;
import com.swhd.content.api.type.wrapper.TypeInfoWrapper;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.oceanengine.dto.result.api.agent.OceanengineApiToolIndustry;
import com.swhd.oauth.api.oceanengine.wrapper.OceanengineAgentApiToolWrapper;
import com.swhd.oauth.api.oceanengine.wrapper.OceanengineAgentOauthAccountWrapper;
import com.swhd.user.api.tenant.wrapper.TenantUserInfoWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/12/4
 */
@Service
@AllArgsConstructor
public class AccountOceanengineInfoService {

    /**
     * 填充账户信息
     *
     * @param list 列表
     */
    public void fillAccountInfo(List<AccountOceanengineInfoResultVo> list) {
        fillAccountInfo(list, true);
    }

    /**
     * 填充账户信息
     *
     * @param list 列表
     */
    public void fillAccountInfo(List<AccountOceanengineInfoResultVo> list, boolean fillCustom) {
        // 设置账户类型
        TypeInfoWrapper.getInstance().setList(list, AccountOceanengineInfoResultVo::getAccountTypeId,
                AccountOceanengineInfoResultVo::setAccountType);
        // 设置代理商信息
        OceanengineAgentOauthAccountWrapper.getInstance().setListByAgentId(list,
                AccountOceanengineInfoResultVo::getAgentId, AccountOceanengineInfoResultVo::setAgent);
        if (fillCustom) {
            // 设置客户信息
            CustomInfoWrapper.getInstance().setList(list, AccountOceanengineInfoResultVo::getCustomId,
                    AccountOceanengineInfoResultVo::setCustomInfo);
            // 设置客户渠道信息
            CustomInfoWrapper.getInstance().setList(list, AccountOceanengineInfoResultVo::getCustomChannelId,
                    AccountOceanengineInfoResultVo::setCustomChannelInfo);
        }
        // 设置行业名称
        Map<Long, OceanengineApiToolIndustry> industryMap = OceanengineAgentApiToolWrapper.getInstance().advertiserIndustryMap();
        list.forEach(vo -> {
            vo.setFirstIndustryName(getIndustryName(industryMap, vo.getFirstIndustryId()));
            vo.setSecondIndustryName(getIndustryName(industryMap, vo.getSecondIndustryId()));
        });
        // 设置优化师
        TenantUserInfoWrapper.getInstance().setList(list, AccountOceanengineInfoResultVo::getOptimizationEngineer,
                (vo, userInfo) -> vo.setOptimizationEngineerName(userInfo.getNickname()));

        //设置商务人员
        TenantUserInfoWrapper.getInstance().setListOfIds(list, AccountOceanengineInfoResultVo::getBusinessPersonnelList, AccountOceanengineInfoResultVo::setBusinessPersonnelAccountList);
    }

    private String getIndustryName(Map<Long, OceanengineApiToolIndustry> industryMap, Long industryId) {
        return Optional.ofNullable(industryMap.get(industryId)).map(OceanengineApiToolIndustry::getIndustryName).orElse(null);
    }

    /**
     * 设置广告主账户信息到列表对象里
     */
    public void setAccountInfo(List<? extends AccountOceanengineInfoSet> list) {
        setAccountInfo(list, true);
    }

    /**
     * 设置广告主账户信息到列表对象里
     */
    public void setAccountInfo(List<? extends AccountOceanengineInfoSet> list, boolean fillCustom) {
        AccountOceanengineInfoWrapper.getInstance().setListByAdvertiserId(list,
                AccountOceanengineInfoSet::getAdvertiserId,
                (vo, account) -> vo.setAccount(Func.copy(account, AccountOceanengineInfoResultVo.class)));
        List<AccountOceanengineInfoResultVo> accountList = list.stream()
                .map(AccountOceanengineInfoSet::getAccount)
                .filter(Objects::nonNull)
                .toList();
        fillAccountInfo(accountList, fillCustom);
    }

}
