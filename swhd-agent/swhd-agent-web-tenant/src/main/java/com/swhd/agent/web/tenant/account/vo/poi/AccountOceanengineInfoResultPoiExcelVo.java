package com.swhd.agent.web.tenant.account.vo.poi;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineInfoResultVo;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import com.swhd.magiccube.core.constant.BigDecimalConstant;
import com.swhd.magiccube.easypoi.interfaces.IPoiIndex;
import com.swhd.magiccube.tool.DateTimeUtil;
import com.swhd.oauth.api.oceanengine.dto.result.OceanengineAgentOauthAccountResult;
import com.swhd.user.api.tenant.dto.result.TenantUserTenantResult;
import com.swj.magiccube.api.Constant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
@Getter
@Setter
@Schema(description = "AccountOceanengineInfoResultPoiExcelVo对象")
public class AccountOceanengineInfoResultPoiExcelVo extends AccountOceanengineInfoResultVo implements IPoiIndex {

    private Integer index;

    public String getCustomChannelName(){
        CustomInfoResult customInfo = getCustomChannelInfo();
        if (customInfo == null) {
            return null;
        }
        if (customInfo.getCustomType() == null) {
            return customInfo.getName();
        }
        return String.format("%s(%s)", customInfo.getName(), customInfo.getCustomType().getTitle());
    }

    public String getCustomName() {
        CustomInfoResult customInfo = getCustomInfo();
        if (customInfo == null) {
            return null;
        }
        if (customInfo.getCustomType() == null) {
            return customInfo.getName();
        }
        return String.format("%s(%s)", customInfo.getName(), customInfo.getCustomType().getTitle());
    }

    public String getUsingVpnText(){
        if (getUsingVpn() == null){
            return null;
        }
        return getUsingVpn() ? "是" : "否";
    }

    public String getManagedByAgentText(){
        return BooleanUtil.isTrue(getManagedByAgent()) ? "是" : "否";
    }


    /**
     * 创建时间名称
     */
    public String getOceanengineCreateTimeName() {
        return DateTimeUtil.formatDateTime(getOceanengineCreateTime());
    }
    /**
     * 代理商名称
     */
    public String getAgentName() {
        return Optional.ofNullable(getAgent())
                .map(OceanengineAgentOauthAccountResult::getAgentName)
                .orElse(Constant.Str.EMPTY);
    }

    /**
     * 代理商ID
     */
    public String getAgentIdName() {
        return Optional.ofNullable(getAgent())
                .map(OceanengineAgentOauthAccountResult::getAgentId)
                .map(String::valueOf)
                .orElse(Constant.Str.EMPTY);
    }

    /**
     * 行业名称
     */
    public String getIndustryName() {
        return String.format("%s/%s", getFirstIndustryName(), getSecondIndustryName());
    }

    /**
     * 代理商返点比例名称
     */
    public String getChannelRebateName() {
        if (getChannelRebate() == null || getChannelRebate().compareTo(BigDecimal.ZERO) <= 0) {
            return Constant.Str.EMPTY;
        }
        return getChannelRebate() + "%";
    }

    public String getBusinessPersonnelName() {
        if (CollectionUtil.isEmpty(getBusinessPersonnelAccountList())) {
            return null;
        }
        return getBusinessPersonnelAccountList().stream()
                .map(TenantUserTenantResult::getNickname)
                .collect(Collectors.joining("、"));
    }


}
