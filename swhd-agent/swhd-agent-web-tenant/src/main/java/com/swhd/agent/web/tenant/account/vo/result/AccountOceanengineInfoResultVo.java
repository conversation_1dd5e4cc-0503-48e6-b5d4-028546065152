package com.swhd.agent.web.tenant.account.vo.result;

import com.swhd.agent.api.account.dto.result.AccountOceanengineInfoResult;
import com.swhd.content.api.type.dto.result.TypeInfoResult;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import com.swhd.oauth.api.oceanengine.dto.result.OceanengineAgentOauthAccountResult;
import com.swhd.user.api.tenant.dto.result.TenantUserTenantResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
@Getter
@Setter
@Schema(description = "AccountOceanengineResultVo对象")
public class AccountOceanengineInfoResultVo extends AccountOceanengineInfoResult {

    @Schema(description = "代理商信息")
    private OceanengineAgentOauthAccountResult agent;

    @Schema(description = "客户信息")
    private CustomInfoResult customInfo;

    @Schema(description = "客户渠道信息")
    private CustomInfoResult customChannelInfo;

    @Schema(description = "账户类型")
    private TypeInfoResult accountType;

    @Schema(description = "一级行业名称")
    private String firstIndustryName;

    @Schema(description = "二级行业名称")
    private String secondIndustryName;

    @Schema(description = "优化师名称")
    private String optimizationEngineerName;

    @Schema(description = "商务人员列表")
    private List<TenantUserTenantResult> businessPersonnelAccountList;

}
