package com.swhd.agent.service.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.MagiccubeHdLambdaQueryChainWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.account.dto.param.company.AccountOceanengineCompanyAddParam;
import com.swhd.agent.api.account.dto.param.company.AccountOceanengineCompanyBatchAddParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoBatchSaveParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoListParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoPageParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoSaveParam;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.mapper.AccountOceanengineInfoMapper;
import com.swhd.agent.service.account.service.AccountOceanengineCompanyService;
import com.swhd.agent.service.account.service.AccountOceanengineInfoService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.oauth.api.oceanengine.client.OceanengineAgentApiToolClient;
import com.swhd.oauth.api.oceanengine.dto.result.api.agent.OceanengineApiToolIndustryFirstResult;
import com.swhd.oauth.api.oceanengine.utils.OceanengineApiToolIndustryUtil;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.mp.util.SortOrderUtil;
import com.swj.magiccube.tool.bean.BeanUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 巨量引擎的账户（广告主）表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@AllArgsConstructor
public class AccountOceanengineInfoServiceImpl extends BaseHdServiceImpl<AccountOceanengineInfoMapper, AccountOceanengineInfo>
        implements AccountOceanengineInfoService {

    private final ThreadLocal<List<OceanengineApiToolIndustryFirstResult>> INDUSTRY_LIST_THREAD_LOCAL = new ThreadLocal<>();

    private final AccountOceanengineCompanyService accountOceanengineCompanyService;

    private final OceanengineAgentApiToolClient oceanengineAgentApiToolClient;

    @Override
    public IPage<AccountOceanengineInfo> page(AccountOceanengineInfoPageParam param) {
        return buildQueryWrapper(BeanUtil.copy(param, AccountOceanengineInfoListParam.class))
                .page(convertToPage(param));
    }

    @Override
    public List<AccountOceanengineInfo> list(AccountOceanengineInfoListParam param) {
        return buildQueryWrapper(param)
                .list();
    }

    /**
     * 构建查询条件
     *
     * @param param 查询参数
     * @return LambdaQueryWrapper
     */
    private MagiccubeHdLambdaQueryChainWrapper<AccountOceanengineInfo> buildQueryWrapper(AccountOceanengineInfoListParam param) {
        MagiccubeHdLambdaQueryChainWrapper<AccountOceanengineInfo> queryWrapper = lambdaQuery()
                .eq(Func.isNotEmpty(param.getAgentId()), AccountOceanengineInfo::getAgentId, param.getAgentId())
                .eq(Func.isNotEmpty(param.getAdvertiserId()), AccountOceanengineInfo::getAdvertiserId, param.getAdvertiserId())
                .eq(Func.isNotEmpty(param.getManagedByAgent()), AccountOceanengineInfo::getManagedByAgent, param.getManagedByAgent())
                .eq(Func.isNotEmpty(param.getUsingVpn()), AccountOceanengineInfo::getUsingVpn, param.getUsingVpn())
                .in(Func.isNotEmpty(param.getAdvertiserIds()), AccountOceanengineInfo::getAdvertiserId, param.getAdvertiserIds())
                .like(Func.isNotEmpty(param.getAdvertiserName()), AccountOceanengineInfo::getAdvertiserName, param.getAdvertiserName())
                .eq(Func.isNotEmpty(param.getAdvertiserCompanyId()),
                        AccountOceanengineInfo::getAdvertiserCompanyId, param.getAdvertiserCompanyId())
                .in(Func.isNotEmpty(param.getAdvertiserCompanyIds()),
                        AccountOceanengineInfo::getAdvertiserCompanyId, param.getAdvertiserCompanyIds())
                .like(Func.isNotEmpty(param.getAdvertiserCompanyName()),
                        AccountOceanengineInfo::getAdvertiserCompanyName, param.getAdvertiserCompanyName())
                .eq(Func.isNotEmpty(param.getFirstIndustryId()), AccountOceanengineInfo::getFirstIndustryId, param.getFirstIndustryId())
                .eq(Func.isNotEmpty(param.getSecondIndustryId()), AccountOceanengineInfo::getSecondIndustryId, param.getSecondIndustryId())
                .eq(Func.isNotEmpty(param.getSelfOperationTag()), AccountOceanengineInfo::getSelfOperationTag, param.getSelfOperationTag())
                .eq(Func.isNotEmpty(param.getAccountTypeId()), AccountOceanengineInfo::getAccountTypeId, param.getAccountTypeId())
                .eq(Func.isNotEmpty(param.getCustomId()), AccountOceanengineInfo::getCustomId, param.getCustomId())
                .eq(Func.isNotEmpty(param.getCustomChannelId()), AccountOceanengineInfo::getCustomChannelId, param.getCustomChannelId())
                .eq(Func.isNotEmpty(param.getOptimizationEngineer()), AccountOceanengineInfo::getOptimizationEngineer, param.getOptimizationEngineer())
                .func(Func.isNotEmpty(param.getBusinessPersonnelList()), wrapper ->
                        wrapper.and(andWrapper -> param.getBusinessPersonnelList().forEach(personnelId ->
                                andWrapper.or(orWrapper -> orWrapper.jsonArrayContains(AccountOceanengineInfo::getBusinessPersonnelList, personnelId)))))
                .betweenDateTimeList(param.getOceanengineCreateTimeBetween(), AccountOceanengineInfo::getOceanengineCreateTime);

        if (Func.isNotEmpty(param.getSort())) {
            String sqlSegment = SortOrderUtil.normalizeSqlSegment(param.getSort());
            queryWrapper.last("order by" + sqlSegment);
        } else {
            queryWrapper.orderByDesc(AccountOceanengineInfo::getOceanengineCreateTime)
                    .orderByDesc(AccountOceanengineInfo::getId);
        }

        return queryWrapper;
    }

    @Override
    public AccountOceanengineInfo getByAdvertiserId(Long advertiserId) {
        return lambdaQuery().eq(AccountOceanengineInfo::getAdvertiserId, advertiserId).limitOne();
    }

    @Override
    public List<AccountOceanengineInfo> listByAdvertiserIds(Collection<Long> advertiserIds) {
        if (Func.isEmpty(advertiserIds)) {
            return Collections.emptyList();
        }
        List<AccountOceanengineInfo> list = new ArrayList<>();
        Func.executeBatch(new ArrayList<>(advertiserIds), 1000, subAdvertiserIds -> {
            List<AccountOceanengineInfo> subList = lambdaQuery().in(AccountOceanengineInfo::getAdvertiserId, subAdvertiserIds).list();
            list.addAll(subList);
        });
        return list;
    }

    @Override
    public List<AccountOceanengineInfo> listByCompanyId(Long advertiserCompanyId) {
        return lambdaQuery().eq(AccountOceanengineInfo::getAdvertiserCompanyId, advertiserCompanyId).list();
    }

    @Override
    public boolean existsByAdvertiserId(Long advertiserId) {
        return lambdaQuery().eq(AccountOceanengineInfo::getAdvertiserId, advertiserId).exists();
    }

    @Override
    public List<Long> existsByAdvertiserIds(List<Long> advertiserIds) {
        if (Func.isEmpty(advertiserIds)) {
            return Collections.emptyList();
        }
        List<Long> existsByAdvertiserIds = new ArrayList<>();
        Func.executeBatch(advertiserIds, 1000, subAdvertiserIds -> {
            List<AccountOceanengineInfo> list = lambdaQuery()
                    .select(List.of(AccountOceanengineInfo::getAdvertiserId))
                    .in(AccountOceanengineInfo::getAdvertiserId, subAdvertiserIds)
                    .list();
            existsByAdvertiserIds.addAll(list.stream().map(AccountOceanengineInfo::getAdvertiserId).distinct().toList());
        });
        return existsByAdvertiserIds;
    }

    @Override
    @Lockable(prefixKey = "agent:account:oceanengine:save", waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public void save(AccountOceanengineInfoSaveParam param) {
        try {
            AccountOceanengineInfo old = getByAdvertiserId(param.getAdvertiserId());
            AccountOceanengineInfo save = saveParamToOceanengineInfo(param);
            if (old != null) {
                save.setId(old.getId());
                updateById(save);
            } else {
                save(save);
            }
            if (param.getAdvertiserCompanyId() != null) {
                AccountOceanengineCompanyAddParam companyAddParam = new AccountOceanengineCompanyAddParam();
                companyAddParam.setCompanyId(param.getAdvertiserCompanyId());
                companyAddParam.setCompanyName(param.getAdvertiserCompanyName());
                accountOceanengineCompanyService.addOrUpdate(companyAddParam);
            }
        } finally {
            INDUSTRY_LIST_THREAD_LOCAL.remove();
        }
    }

    @Override
    @Lockable(prefixKey = "agent:account:oceanengine:save", waitTime = 6000)
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(AccountOceanengineInfoBatchSaveParam param) {
        try {
            List<AccountOceanengineInfoSaveParam> list = param.getList().stream().distinct().toList();
            List<Long> advertiserIds = list.stream()
                    .map(AccountOceanengineInfoSaveParam::getAdvertiserId)
                    .toList();
            List<AccountOceanengineInfo> existList = listByAdvertiserIds(advertiserIds);
            List<Long> existAdvertiserIds = existList.stream()
                    .map(AccountOceanengineInfo::getAdvertiserId)
                    .toList();

            List<AccountOceanengineInfo> saveList = list.stream()
                    .filter(saveParam -> !existAdvertiserIds.contains(saveParam.getAdvertiserId()))
                    .map(this::saveParamToOceanengineInfo)
                    .toList();
            if (Func.isNotEmpty(saveList)) {
                saveBatch(saveList);
            }

            List<AccountOceanengineInfo> updateList = list.stream()
                    .map(saveParam -> existList.stream()
                            .filter(exist -> Objects.equals(exist.getAdvertiserId(), saveParam.getAdvertiserId()))
                            .findFirst()
                            .map(exist -> saveParamToOceanengineInfo(saveParam)
                                    .<AccountOceanengineInfo>setId(exist.getId()))
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .toList();
            if (Func.isNotEmpty(updateList)) {
                updateBatchById(updateList);
            }

            // 保存公司信息
            List<AccountOceanengineCompanyAddParam> companyAddParamList = saveList.stream()
                    .filter(info -> info.getAdvertiserCompanyId() != null)
                    .filter(info -> Objects.equals(info.getAdvertiserCompanyId(), 0L))
                    .map(info -> {
                        AccountOceanengineCompanyAddParam companyAddParam = new AccountOceanengineCompanyAddParam();
                        companyAddParam.setCompanyId(info.getAdvertiserCompanyId());
                        companyAddParam.setCompanyName(info.getAdvertiserCompanyName());
                        return companyAddParam;
                    })
                    .toList();
            if (Func.isNotEmpty(companyAddParamList)) {
                accountOceanengineCompanyService.batchAddOrUpdate(new AccountOceanengineCompanyBatchAddParam()
                        .setList(companyAddParamList));
            }
        } finally {
            INDUSTRY_LIST_THREAD_LOCAL.remove();
        }
    }

    private AccountOceanengineInfo saveParamToOceanengineInfo(AccountOceanengineInfoSaveParam saveParam) {
        AccountOceanengineInfo oceanengineInfo = Func.copy(saveParam, AccountOceanengineInfo.class);
        if (oceanengineInfo.getFirstIndustryId() == null && Func.isNotEmpty(saveParam.getFirstIndustryName())) {
            Long firstIndustryId = OceanengineApiToolIndustryUtil.getFirstIndustryIdByName(
                    industryList(), saveParam.getFirstIndustryName());
            oceanengineInfo.setFirstIndustryId(firstIndustryId);
        }
        if (oceanengineInfo.getSecondIndustryId() == null && Func.isNotEmpty(saveParam.getSecondIndustryName())) {
            Long secondIndustryId = OceanengineApiToolIndustryUtil.getSecondIndustryIdByName(
                    industryList(), oceanengineInfo.getFirstIndustryId(), saveParam.getSecondIndustryName());
            oceanengineInfo.setSecondIndustryId(secondIndustryId);
        }
        return oceanengineInfo;
    }

    private List<OceanengineApiToolIndustryFirstResult> industryList() {
        List<OceanengineApiToolIndustryFirstResult> list = INDUSTRY_LIST_THREAD_LOCAL.get();
        if (list == null) {
            Rsp<List<OceanengineApiToolIndustryFirstResult>> listRsp = oceanengineAgentApiToolClient.advertiserIndustryList();
            RspHd.failThrowException(listRsp);
            list = listRsp.getData();
            INDUSTRY_LIST_THREAD_LOCAL.set(list);
        }
        return list;
    }

}
