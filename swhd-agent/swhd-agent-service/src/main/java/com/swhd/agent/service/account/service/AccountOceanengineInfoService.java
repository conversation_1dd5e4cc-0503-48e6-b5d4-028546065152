package com.swhd.agent.service.account.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoBatchSaveParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoListParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoPageParam;
import com.swhd.agent.api.account.dto.param.info.AccountOceanengineInfoSaveParam;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.magiccube.mybatis.base.IBaseHdService;

import java.util.Collection;
import java.util.List;

/**
 * 巨量引擎的账户（广告主）表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
public interface AccountOceanengineInfoService extends IBaseHdService<AccountOceanengineInfo> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<AccountOceanengineInfo> page(AccountOceanengineInfoPageParam param);

    /**
     * 根据广告主账户id获取
     *
     * @param advertiserId 广告主账户id
     * @return AccountOceanengine
     */
    AccountOceanengineInfo getByAdvertiserId(Long advertiserId);

    /**
     * 根据广告主账户id获取
     *
     * @param advertiserIds 广告主账户id列表
     * @return List
     */
    List<AccountOceanengineInfo> listByAdvertiserIds(Collection<Long> advertiserIds);

    /**
     * 根据广告主公司id获取
     *
     * @param advertiserCompanyId 广告主公司id
     * @return List
     */
    List<AccountOceanengineInfo> listByCompanyId(Long advertiserCompanyId);

    /**
     * 判断广告主账户id是否存在数据库里
     *
     * @param advertiserId 广告主账户id
     * @return 是否存在：false-不存在，true-存在
     */
    boolean existsByAdvertiserId(Long advertiserId);

    /**
     * 判断广告主账户id是否存在库里，返回数据库存在的广告主账户id列表
     *
     * @param advertiserIds 广告主账户id列表
     * @return 数据库存在的广告主账户id列表
     */
    List<Long> existsByAdvertiserIds(List<Long> advertiserIds);

    /**
     * 保存
     *
     * @param param 参数
     */
    void save(AccountOceanengineInfoSaveParam param);

    /**
     * 批量保存
     *
     * @param param 参数
     */
    void batchSave(AccountOceanengineInfoBatchSaveParam param);

    /**
     * 列表查询
     *
     * @param param 查询参数
     * @return List
     */
    List<AccountOceanengineInfo> list(AccountOceanengineInfoListParam param);

}
