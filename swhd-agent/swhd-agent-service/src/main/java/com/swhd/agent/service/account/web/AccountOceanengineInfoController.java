package com.swhd.agent.service.account.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.agent.api.account.client.AccountOceanengineInfoClient;
import com.swhd.agent.api.account.dto.param.info.*;
import com.swhd.agent.api.account.dto.result.AccountOceanengineInfoResult;
import com.swhd.agent.service.account.entity.AccountOceanengineInfo;
import com.swhd.agent.service.account.service.AccountOceanengineInfoService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-28
 */
@Slf4j
@RestController
@RequestMapping(AccountOceanengineInfoClient.BASE_PATH)
public class AccountOceanengineInfoController implements AccountOceanengineInfoClient {

    @Autowired
    private AccountOceanengineInfoService accountOceanengineInfoService;

    @Override
    public Rsp<PageResult<AccountOceanengineInfoResult>> page(AccountOceanengineInfoPageParam param) {
        IPage<AccountOceanengineInfo> iPage = accountOceanengineInfoService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, AccountOceanengineInfoResult.class));
    }

    @Override
    public Rsp<AccountOceanengineInfoResult> getById(Long id) {
        AccountOceanengineInfo entity = accountOceanengineInfoService.getById(id);
        return RspHd.data(Func.copy(entity, AccountOceanengineInfoResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineInfoResult>> listByIds(Collection<Long> ids) {
        List<AccountOceanengineInfo> list = accountOceanengineInfoService.listByIds(ids);
        return RspHd.data(Func.copy(list, AccountOceanengineInfoResult.class));
    }

    @Override
    public Rsp<List<AccountOceanengineInfoResult>> listByAdvertiserIds(Collection<Long> advertiserIds) {
        List<AccountOceanengineInfo> list = accountOceanengineInfoService.listByAdvertiserIds(advertiserIds);
        return RspHd.data(Func.copy(list, AccountOceanengineInfoResult.class));
    }

    @Override
    public Rsp<AccountOceanengineInfoResult> getByAdvertiserId(Long advertiserId) {
        AccountOceanengineInfo info = accountOceanengineInfoService.getByAdvertiserId(advertiserId);
        return Rsp.data(Func.copy(info, AccountOceanengineInfoResult.class));
    }

    @Override
    public Rsp<Void> update(AccountOceanengineInfoUpdateParam param) {
        boolean result = accountOceanengineInfoService.updateById(Func.copy(param, AccountOceanengineInfo.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> batchUpdate(AccountOceanengineInfoBatchUpdateParam param) {
        AccountOceanengineInfo update = Func.copy(param, AccountOceanengineInfo.class);
        boolean result = accountOceanengineInfoService.lambdaUpdate()
                .in(AccountOceanengineInfo::getId, param.getIds())
                .update(update);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> batchUpdateByAdvertiserIds(AccountOceanengineInfoBatchUpdateByAdvertiserIdsParam param) {
        AccountOceanengineInfo update = Func.copy(param, AccountOceanengineInfo.class);
        boolean result = accountOceanengineInfoService.lambdaUpdate()
                .in(AccountOceanengineInfo::getAdvertiserId, param.getAdvertiserIds())
                .update(update);
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> batchUpdateByCustomId(AccountOceanengineInfoBatchUpdateByCustomIdParam param) {
        AccountOceanengineInfo update = Func.copy(param, AccountOceanengineInfo.class);
        boolean result = accountOceanengineInfoService.lambdaUpdate()
                .eq(AccountOceanengineInfo::getCustomId, param.getCustomId())
                .update(update);
        return RspHd.status(result);
    }

    @Override
    public Rsp<List<AccountOceanengineInfoResult>> list(AccountOceanengineInfoListParam param) {
        List<AccountOceanengineInfo> list = accountOceanengineInfoService.list(param);
        return RspHd.data(Func.copy(list, AccountOceanengineInfoResult.class));
    }

}
