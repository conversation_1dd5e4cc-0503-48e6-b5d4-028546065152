package com.swhd.agent.api.account.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-28
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "AccountOceanengineInfoBatchUpdateByCustomIdsParam对象")
public class AccountOceanengineInfoBatchUpdateByCustomIdsParam {

    @Schema(description = "客户id列表")
    @NotEmpty(message = "客户id列表不能为空")
    private List<Long> customIds;

    @Schema(description = "一级行业id")
    private Long firstIndustryId;

    @Schema(description = "二级行业id")
    private Long secondIndustryId;

    @Schema(description = "账户类型id：tagent_account_type#id")
    private Long accountTypeId;

    @Schema(description = "客户id：tagent_custom_info#id")
    private Long customId;

    @Schema(description = "客户id：tagent_custom_info#id")
    private Long customChannelId;

    @Schema(description = "优化师")
    private Long optimizationEngineer;

    @Schema(description = "是否代运营")
    private Boolean managedByAgent;

    @Schema(description = "是否使用VPN")
    private Boolean usingVpn;

    @Schema(description = "商务人员列表")
    private List<Long> businessPersonnelList;

    @Schema(description = "渠道返点")
    private BigDecimal channelRebate;

}
